<template>
  <el-dialog
    title="开通免费培训课程"
    :visible="visible"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-row :gutter="10">
      <el-col :span="6">
        <div class="content-left">
          <div class="tree-title">
            <span>资源分类</span>
            <el-button
              v-if="selectedCategoryName"
              type="text"
              size="mini"
              icon="el-icon-close"
              @click="clearCategorySelection"
            >
              取消选择
            </el-button>
          </div>
          <el-scrollbar wrap-class="default-scrollbar__wrap" view-class="p20-scrollbar__view" class="content-container" style="height: 400px;">
            <Tree
              ref="freeCourseTree"
              :type="categoryType"
              :platform-type="1"
              :highlight-current="true"
              @nodeClick="handleTreeNodeClick"
            />
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="18">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="教程" name="course">
            <div class="search-column">
              <div class="search-column__item">
                <el-input
                  v-model="courseQuery.condition.keyword"
                  placeholder="请输入教程名称"
                  clearable
                  @change="searchCourse"
                >
                  <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="searchCourse" />
                </el-input>
              </div>
              <div class="search-column__item">
                <el-select v-model="courseQuery.condition.memberLevel" placeholder="会员等级" clearable @change="searchCourse">
                  <el-option
                    v-for="level in memberLevelList"
                    :key="level.level"
                    :label="level.name"
                    :value="level.level"
                  />
                </el-select>
              </div>
            </div>
            <el-table :data="courseList" border stripe @selection-change="onCourseSelectChange">
              <el-table-column type="selection" />
              <el-table-column prop="memberLevelName" label="会员等级" />
              <el-table-column prop="name" label="教程名称" />
              <el-table-column prop="cateName" label="分类">
                <template slot-scope="scope">
                  <el-tooltip
                    :content="scope.row.cateName"
                    placement="top"
                    :disabled="!scope.row.cateName || scope.row.cateName.length <= 24"
                    effect="dark"
                  >
                    <div class="introduction-text">
                      {{ scope.row.cateName | filterText }}
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="duration" label="时长" />
              <el-table-column label="教程简介" width="200">
                <template slot-scope="scope">
                  <el-tooltip
                    :content="scope.row.introduction"
                    placement="top"
                    :disabled="!scope.row.introduction || scope.row.introduction.length <= 24"
                    effect="dark"
                  >
                    <div class="introduction-text">
                      {{ scope.row.introduction | filterText }}
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
            <Pagination
              class="text-center"
              :total="courseTotal"
              :page="courseQuery.pager.page"
              :page-size="courseQuery.pager.pageSize"
              @pagination="handleCoursePagination"
            />
          </el-tab-pane>
          <el-tab-pane label="SOP" name="sop">
            <div class="search-column">
              <div class="search-column__item">
                <el-input
                  v-model="sopQuery.condition.keyword"
                  placeholder="请输入SOP名称"
                  clearable
                  @change="searchSop"
                >
                  <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="searchSop" />
                </el-input>
              </div>
              <div class="search-column__item">
                <el-select v-model="sopQuery.condition.memberLevel" placeholder="会员等级" clearable @change="searchSop">
                  <el-option
                    v-for="level in memberLevelList"
                    :key="'sop-' + level.level"
                    :label="level.name"
                    :value="level.level"
                  />
                </el-select>
              </div>
            </div>
            <el-table :data="sopList" border stripe max-height="300px" @selection-change="onSopSelectChange">
              <el-table-column type="selection" />
              <el-table-column prop="memberLevelName" label="会员等级" />
              <el-table-column prop="name" label="SOP名称" />
              <el-table-column prop="cateName" label="分类">
                <template slot-scope="scope">
                  <el-tooltip
                    :content="scope.row.cateName"
                    placement="top"
                    :disabled="!scope.row.cateName || scope.row.cateName.length <= 24"
                    effect="dark"
                  >
                    <div class="introduction-text">
                      {{ scope.row.cateName | filterText }}
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
            <Pagination
              class="text-center"
              :total="sopTotal"
              :page="sopQuery.pager.page"
              :page-size="sopQuery.pager.pageSize"
              @pagination="handleSopPagination"
            />
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import Tree from '@/pages/validManage/components/platformTree'
import Pagination from '@/components/Pagination'
import { getCourseList, getSopList, addFreeResource } from '@/api/freeCourse'
import { getVipLevelList } from '@/api/vip'

export default {
  name: 'DialogFreeCourse',
  components: {
    Tree,
    Pagination
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    orgData: {
      type: Object,
      default: () => ({})
    }
  },
  filters: {
    filterText(value) {
      if (!value) return '-'
      return value.length > 24 ? value.slice(0, 24) + '...' : value
    }
  },
  data() {
    return {
      visible: this.show,
      activeTab: 'course',
      categoryType: 2, // 2-课程，3-sop
      selectedCourses: [],
      selectedSops: [],
      memberLevelList: [], // 会员等级列表
      selectedCategoryName: '', // 当前选中的分类名称
      // 教程相关
      courseList: [],
      courseTotal: 0,
      courseQuery: {
        condition: {
          keyword: '',
          memberLevel: null,
          platform: 1,
          status: 2,
          type: 1,
          cateId: null,
          orgId: null,
          isExcludeExistResource: 1
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // SOP相关
      sopList: [],
      sopTotal: 0,
      sopQuery: {
        condition: {
          keyword: '',
          memberLevel: null,
          platform: 1,
          status: 2,
          type: 1,
          cateId: null,
          orgId: null,
          isExcludeExistResource: 1
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      }
    }
  },
  watch: {
    show(val) {
      this.visible = val
      if (val && this.orgData.orgId) {
        this.initData()
      }
    },
    orgData: {
      handler(val) {
        if (val.orgId && this.visible) {
          this.initData()
        }
      },
      deep: true
    }
  },
  mounted() {
    this.loadMemberLevels()
  },
  beforeDestroy() {
    // 组件销毁前清理
    this.selectedCourses = []
    this.selectedSops = []
    this.courseList = []
    this.sopList = []
  },
  methods: {
    // 加载会员等级列表
    async loadMemberLevels() {
      try {
        const response = await getVipLevelList()
        this.memberLevelList = response || []
      } catch (error) {
        console.error('获取会员等级列表失败:', error)
      }
    },
    initData() {
      // 设置单位ID
      this.courseQuery.condition.orgId = this.orgData.orgId
      this.sopQuery.condition.orgId = this.orgData.orgId

      // 根据当前tab加载数据
      if (this.activeTab === 'course') {
        this.loadCourseList()
      } else {
        this.loadSopList()
      }
    },
    // 树节点点击
    handleTreeNodeClick(data) {
      const cateId = data.categoryId || null
      this.selectedCategoryName = data.name || ''
      this.courseQuery.condition.cateId = cateId
      this.sopQuery.condition.cateId = cateId

      if (this.activeTab === 'course') {
        this.courseQuery.pager.page = 1
        this.loadCourseList()
      } else {
        this.sopQuery.pager.page = 1
        this.loadSopList()
      }
    },
    // 清除分类选择
    clearCategorySelection() {
      this.selectedCategoryName = ''
      this.courseQuery.condition.cateId = null
      this.sopQuery.condition.cateId = null

      // 使用nextTick确保DOM更新后再操作
      this.$nextTick(() => {
        try {
          // 清除树的选中状态
          if (this.$refs.freeCourseTree && this.$refs.freeCourseTree.$refs.tree) {
            this.$refs.freeCourseTree.$refs.tree.setCurrentKey(null)
          }
        } catch (error) {
          console.warn('清除树选中状态失败:', error)
        }
      })

      if (this.activeTab === 'course') {
        this.courseQuery.pager.page = 1
        this.loadCourseList()
      } else {
        this.sopQuery.pager.page = 1
        this.loadSopList()
      }
    },
    // tab切换
    handleTabClick(tab) {
      this.categoryType = tab.name === 'course' ? 2 : 3
      if (tab.name === 'course') {
        this.loadCourseList()
      } else {
        this.loadSopList()
      }
    },
    // 加载教程列表
    async loadCourseList() {
      try {
        const response = await getCourseList(this.courseQuery)
        response.records.forEach(element => {
          element.memberLevelName = this.memberLevelList.find(i => i.level === element.memberLevel).name || '-'
        })
        this.courseList = response.records || []
        this.courseTotal = response.total || 0
      } catch (error) {
        console.error('获取教程列表失败:', error)
        this.$message.error('获取教程列表失败')
      }
    },
    // 加载SOP列表
    async loadSopList() {
      try {
        const response = await getSopList(this.sopQuery)
        response.records.forEach(element => {
          element.memberLevelName = this.memberLevelList.find(i => i.level === element.memberLevel).name || '-'
        })
        this.sopList = response.records || []
        this.sopTotal = response.total || 0
      } catch (error) {
        console.error('获取SOP列表失败:', error)
        this.$message.error('获取SOP列表失败')
      }
    },
    // 搜索教程
    searchCourse() {
      this.courseQuery.pager.page = 1
      this.loadCourseList()
    },
    // 搜索SOP
    searchSop() {
      this.sopQuery.pager.page = 1
      this.loadSopList()
    },
    // 教程选择变化
    onCourseSelectChange(selection) {
      this.selectedCourses = selection
    },
    // SOP选择变化
    onSopSelectChange(selection) {
      this.selectedSops = selection
    },
    // 教程分页
    handleCoursePagination(pagination) {
      this.courseQuery.pager.page = pagination.page
      this.courseQuery.pager.pageSize = pagination.limit
      this.loadCourseList()
    },
    // SOP分页
    handleSopPagination(pagination) {
      this.sopQuery.pager.page = pagination.page
      this.sopQuery.pager.pageSize = pagination.limit
      this.loadSopList()
    },
    // 取消
    handleCancel() {
      this.handleClose()
    },
    // 确认
    async handleConfirm() {
      const selectedItems = this.activeTab === 'course' ? this.selectedCourses : this.selectedSops
      if (selectedItems.length === 0) {
        this.$message.warning('请选择要开通的资源')
        return
      }

      // 获取当前页面实际显示的数据数量
      const currentPageData = this.activeTab === 'course' ? this.courseList : this.sopList
      const currentPageCount = currentPageData.length

      // 检查是否需要跨页开通
      if (selectedItems.length === currentPageCount && currentPageCount > 0) {
        try {
          await this.$confirm(
            `检测到您选择了当前页面的全部资源（${selectedItems.length}项），是否要开通所有符合筛选条件的资源？`,
            '跨页开通确认',
            {
              confirmButtonText: '是，开通全部',
              cancelButtonText: '否，仅开通已选',
              type: 'warning'
            }
          )
          // 用户选择跨页开通
          await this.performAddFreeResource(selectedItems, true)
        } catch (error) {
          if (error === 'cancel') {
            // 用户选择仅开通已选
            await this.performAddFreeResource(selectedItems, false)
          }
          // 其他错误不处理，让用户重新操作
        }
      } else {
        // 正常开通已选资源
        await this.performAddFreeResource(selectedItems, false)
      }
    },
    // 执行开通免费培训课程
    async performAddFreeResource(selectedItems, isCrossPage = false) {
      try {
        const resourceType = this.activeTab === 'course' ? 1 : 2 // 1-教程 2-sop
        const resourceIdList = selectedItems.map(item => resourceType === 1 ? item.courseId : item.sopId)

        const params = {
          orgId: this.orgData.orgId,
          resourceIdList,
          resourceType,
          dataType: isCrossPage ? 1 : 0
        }

        // 如果是跨页开通，添加筛选条件
        if (isCrossPage) {
          const currentQuery = this.activeTab === 'course' ? this.courseQuery : this.sopQuery
          if (currentQuery.condition.keyword) {
            params.keyword = currentQuery.condition.keyword
          }
          if (currentQuery.condition.cateId) {
            params.cateId = currentQuery.condition.cateId
          }
          if (currentQuery.condition.memberLevel) {
            params.memberLevel = currentQuery.condition.memberLevel
          }
        }

        await addFreeResource(params)

        this.$message.success(
          isCrossPage
            ? `成功开通所有符合条件的${this.activeTab === 'course' ? '教程' : 'SOP'}资源！`
            : `成功开通${selectedItems.length}项${this.activeTab === 'course' ? '教程' : 'SOP'}资源！`
        )

        // 通知父组件刷新数据
        this.$emit('success')
        this.handleClose()
      } catch (error) {
        console.error('开通免费培训课程失败:', error)
        this.$message.error('开通失败，请重试')
      }
    },
    // 关闭弹窗
    handleClose() {
      // 重置数据
      this.selectedCourses = []
      this.selectedSops = []
      this.selectedCategoryName = ''
      this.courseQuery.condition.keyword = ''
      this.sopQuery.condition.keyword = ''
      this.courseQuery.condition.cateId = null
      this.sopQuery.condition.cateId = null
      this.courseQuery.condition.memberLevel = null
      this.sopQuery.condition.memberLevel = null

      // 通知父组件关闭弹窗
      this.$emit('update:show', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.content-left {
  border: 1px solid #dcdfe6;
  border-radius: 4px;

  .tree-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dcdfe6;
    font-weight: bold;
  }

  .introduction-text {
    display: -webkit-box !important;
    -webkit-box-orient: vertical !important;
    -webkit-line-clamp: 2 !important;
    line-clamp: 2 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    line-height: 1.4 !important;
    max-height: 2.8em !important; /* 2行的高度 */
    word-break: break-word !important;
    white-space: normal !important;
    width: 100% !important;
    font-size: 14px !important;
  }

  /* 确保表格单元格也应用样式 */
  ::v-deep .el-table .el-table__body .introduction-text {
    display: -webkit-box !important;
    -webkit-box-orient: vertical !important;
    -webkit-line-clamp: 2 !important;
    line-clamp: 2 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    line-height: 1.4 !important;
    max-height: 2.8em !important;
    word-break: break-word !important;
    white-space: normal !important;
  }

  .content-container {
    padding: 10px;
  }
}

.search-column {
  display: flex;
  margin-bottom: 15px;

  &__item {
    margin-right: 15px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
