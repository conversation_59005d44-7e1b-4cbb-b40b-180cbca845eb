import request from '@/utils/request'

// 获取会员等级列表
export function getVipLevelList() {
  return request({
    url: `/memberPackage/listMemberLevel`,
    method: 'get'
  })
}

// 创建会员套餐
export function createPackage(params) {
  return request({
    url: '/memberPackage/createPackage',
    method: 'post',
    data: params
  })
}

// 获取会员套餐列表
export function listPackage(params) {
  return request({
    url: '/memberPackage/listPackage',
    method: 'post',
    data: params
  })
}

// 更新会员套餐
export function updatePackage(params) {
  return request({
    url: '/memberPackage/updatePackage',
    method: 'post',
    data: params
  })
}
