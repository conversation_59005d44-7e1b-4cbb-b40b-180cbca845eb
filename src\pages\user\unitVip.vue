<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <div class="search-column">
      <div class="fl">
        <div class="search-column__item">
          <el-button type="primary" @click="openMemberDialog()">开通会员</el-button>
        </div>
      </div>
      <div class="fr">
        <div class="search-column__item">
          <div class="search-column__label">单位名称：</div>
          <div class="search-column__inner">
            <el-input
              v-model="tableQuery.condition.keyword"
              clearable
              placeholder="请输入搜索关键字"
              @clear="searchKeyword()"
              @keydown.enter.native="searchKeyword()"
            >
              <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="searchKeyword()" />
            </el-input>
          </div>
        </div>
        <div class="search-column__item">
          会员等级：
          <el-select v-model="tableQuery.condition.memberLevel" clearable placeholder="请选择会员等级" @change="getMemberOrgList">
            <el-option v-for="item in memberLevelList" :key="'m'+item.level" :label="item.name" :value="item.level" />
          </el-select>
        </div>
        <div class="search-column__item">
          状态：
          <el-select v-model="tableQuery.condition.memberStatus" clearable placeholder="请选择状态" @change="getMemberOrgList">
            <el-option v-for="item in statusList" :key="'s'+item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </div>
    </div>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="memberList" border stripe>
      <el-table-column type="selection" width="55" />
      <el-table-column prop="orgId" label="ID" width="100" />
      <el-table-column prop="orgName" label="单位名称" />
      <el-table-column prop="orgTypeName" label="单位类型" width="120" />
      <el-table-column prop="memberLevel" label="会员等级" width="120">
        <template slot-scope="scope">
          {{ getMemberLevelName(scope.row.memberLevel) }}
        </template>
      </el-table-column>
      <el-table-column prop="memberStatus" label="状态" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.memberStatus === 1 ? 'success' : 'danger'">
            {{ scope.row.memberStatus === 1 ? '正常' : '过期' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="memberStartTime" label="会员期限" width="200">
        <template slot-scope="scope">
          {{ scope.row.memberStartTime }} ~ {{ scope.row.memberEndTime }}
        </template>
      </el-table-column>
      <el-table-column prop="creator" label="开通人" width="100" />
      <el-table-column prop="createTime" label="开通时间" width="160">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="300">
        <template slot-scope="{row}">
          <el-button size="mini" type="text" @click="renewalMember(row)">续期/升级</el-button>
          <el-button size="mini" type="text" @click="viewHistory(row)">开通历史</el-button>
          <el-button size="mini" type="text" style="color: #f56c6c;" @click="deleteMember(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination
      class="text-center"
      :layout="layout"
      :total="total"
      :page="tableQuery.pager.page"
      @pagination="handlePagination"
    />

    <!-- 开通会员弹窗 -->
    <el-dialog
      :title="memberDialogTitle"
      :visible.sync="memberDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      @close="closeMemberDialog"
    >
      <el-form ref="memberForm" :model="memberForm" :rules="memberRules" label-width="120px">
        <el-form-item v-if="!isRenewal" label="选择单位" prop="orgIdList">
          <el-button @click="selectOrgDialogVisible = true">选择单位</el-button>
          <div v-if="selectedOrgs.length > 0" style="margin-top: 10px;">
            <el-tag
              v-for="org in selectedOrgs"
              :key="org.orgId"
              closable
              style="margin-right: 8px; margin-bottom: 8px;"
              @close="removeSelectedOrg(org)"
            >
              {{ org.orgName }}
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="会员等级" prop="memberLevel">
          <el-select v-model="memberForm.memberLevel" placeholder="请选择会员等级" @change="handleMemberLevelChange">
            <el-option v-for="item in memberLevelList" :key="item.level" :label="item.name" :value="item.level" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="memberForm.memberLevel > 0" label="会员期限" prop="memberStartTime">
          <el-col :span="11">
            <el-date-picker
              v-model="memberForm.memberStartTime"
              type="date"
              placeholder="开始日期"
              value-format="yyyy-MM-dd"
              style="width: 100%;"
            />
          </el-col>
          <el-col class="line" :span="2" style="text-align: center;">至</el-col>
          <el-col :span="11">
            <el-date-picker
              v-model="memberForm.memberEndTime"
              type="date"
              placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 100%;"
            />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeMemberDialog">取消</el-button>
        <el-button type="primary" @click="submitMemberForm" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>

    <!-- 选择单位弹窗 -->
    <el-dialog
      title="选择单位"
      :visible.sync="selectOrgDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="search-column" style="margin-bottom: 20px;">
        <div class="search-column__item">
          <el-input
            v-model="orgQuery.condition.keyword"
            placeholder="请输入单位名称"
            clearable
            @keydown.enter.native="getOrgList"
          >
            <el-button slot="append" icon="el-icon-search" @click="getOrgList" />
          </el-input>
        </div>
      </div>
      <el-table
        ref="orgTable"
        v-loading="orgLoading"
        :data="orgList"
        max-height="400"
        border
        stripe
        @selection-change="handleOrgSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="orgId" label="ID" width="80" />
        <el-table-column prop="orgName" label="单位名称" />
        <el-table-column prop="orgTypeName" label="单位类型" width="120" />
      </el-table>
      <Pagination
        class="text-center"
        :layout="'prev, pager, next'"
        :total="orgTotal"
        :page="orgQuery.pager.page"
        style="margin-top: 20px;"
        @pagination="handleOrgPagination"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="selectOrgDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSelectOrg">确定</el-button>
      </div>
    </el-dialog>

    <!-- 开通历史弹窗 -->
    <el-dialog
      title="开通历史"
      :visible.sync="historyDialogVisible"
      width="800px"
    >
      <el-table v-loading="historyLoading" :data="historyList" border stripe>
        <el-table-column prop="memberLevel" label="会员等级" width="120">
          <template slot-scope="scope">
            {{ getMemberLevelName(scope.row.memberLevel) }}
          </template>
        </el-table-column>
        <el-table-column prop="memberStartTime" label="会员期限" width="200">
          <template slot-scope="scope">
            {{ scope.row.memberStartTime }} ~ {{ scope.row.memberEndTime }}
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="开通人" />
        <el-table-column prop="createTime" label="开通时间" width="160">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getVipLevelList } from '@/api/vip'
import { findOrganListPage } from '@/api/organization'
import { memberOrgPage, addMemberOrg, renewalMemberOrg, deleteMemberOrg, getActiveLog } from '@/api/memberOrg'

export default {
  name: 'UnitVip',
  components: {
    Pagination
  },
  data() {
    return {
      loading: false,
      layout: 'total, sizes, prev, pager, next, jumper',

      // 表格查询参数
      tableQuery: {
        condition: {
          keyword: '',
          memberLevel: null,
          memberStatus: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },

      // 表格数据
      memberList: [],
      total: 0,

      // 会员等级列表
      memberLevelList: [],

      // 状态列表
      statusList: [
        { label: '正常', value: 1 },
        { label: '过期', value: 0 }
      ],

      // 开通会员弹窗
      memberDialogVisible: false,
      memberDialogTitle: '开通会员',
      isRenewal: false, // 是否为续期/升级
      submitLoading: false,
      memberForm: {
        orgId: null,
        orgIdList: [],
        memberLevel: null,
        memberStartTime: '',
        memberEndTime: ''
      },
      memberRules: {
        orgIdList: [
          { required: true, message: '请选择单位', trigger: 'change' }
        ],
        memberLevel: [
          { required: true, message: '请选择会员等级', trigger: 'change' }
        ],
        memberStartTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        memberEndTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ]
      },

      // 选择单位相关
      selectOrgDialogVisible: false,
      selectedOrgs: [],
      tempSelectedOrgs: [],
      orgLoading: false,
      orgList: [],
      orgTotal: 0,
      orgQuery: {
        condition: {
          keyword: '',
          status: 1 // 只查询启用的单位
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },

      // 开通历史相关
      historyDialogVisible: false,
      historyLoading: false,
      historyList: [],
      currentOrgId: null
    }
  },

  created() {
    this.getMemberLevelList()
    this.getMemberOrgList()
  },

  methods: {
    // 获取会员等级列表
    async getMemberLevelList() {
      try {
        const response = await getVipLevelList({ filterFree: true })
        this.memberLevelList = response || []
      } catch (error) {
        this.$message.error('获取会员等级列表失败')
      }
    },

    // 获取单位会员列表
    async getMemberOrgList() {
      this.loading = true
      try {
        const response = await memberOrgPage(this.tableQuery)
        this.memberList = response.records || []
        this.total = response.total || 0
      } catch (error) {
        this.$message.error('获取单位会员列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    searchKeyword() {
      this.tableQuery.pager.page = 1
      this.getMemberOrgList()
    },

    // 分页处理
    handlePagination(pagination) {
      this.tableQuery.pager = pagination
      this.getMemberOrgList()
    },

    // 获取会员等级名称
    getMemberLevelName(level) {
      const memberLevel = this.memberLevelList.find(item => item.level === level)
      return memberLevel ? memberLevel.name : '未知'
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      return dateTime.replace('T', ' ').substring(0, 19)
    },

    // 开通会员
    openMemberDialog() {
      this.memberDialogTitle = '开通会员'
      this.isRenewal = false
      this.resetMemberForm()
      this.memberDialogVisible = true
    },

    // 续期/升级会员
    renewalMember(row) {
      this.memberDialogTitle = '续期/升级会员'
      this.isRenewal = true
      this.memberForm = {
        orgId: row.orgId,
        orgIdList: [],
        memberLevel: row.memberLevel,
        memberStartTime: row.memberStartTime,
        memberEndTime: row.memberEndTime
      }
      this.memberDialogVisible = true
    },

    // 重置会员表单
    resetMemberForm() {
      this.memberForm = {
        orgId: null,
        orgIdList: [],
        memberLevel: null,
        memberStartTime: '',
        memberEndTime: ''
      }
      this.selectedOrgs = []
      if (this.$refs.memberForm) {
        this.$refs.memberForm.clearValidate()
      }
    },

    // 关闭会员弹窗
    closeMemberDialog() {
      this.memberDialogVisible = false
      this.resetMemberForm()
    },

    // 会员等级变化处理
    handleMemberLevelChange(level) {
      if (level && level > 0) {
        // 付费会员，设置默认时限（当前日期开始，+364天结束）
        const today = new Date()
        const endDate = new Date(today)
        endDate.setDate(today.getDate() + 364)

        this.memberForm.memberStartTime = this.formatDate(today)
        this.memberForm.memberEndTime = this.formatDate(endDate)
      } else {
        // 免费会员，清空时限
        this.memberForm.memberStartTime = ''
        this.memberForm.memberEndTime = ''
      }
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 提交会员表单
    submitMemberForm() {
      this.$refs.memberForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            const params = { ...this.memberForm }

            if (this.isRenewal) {
              // 续期/升级
              await renewalMemberOrg(params)
              this.$message.success('续期/升级成功')
            } else {
              // 开通会员
              params.orgIdList = this.selectedOrgs.map(org => org.orgId)
              await addMemberOrg(params)
              this.$message.success('开通成功')
            }

            this.closeMemberDialog()
            this.getMemberOrgList()
          } catch (error) {
            this.$message.error(this.isRenewal ? '续期/升级失败' : '开通失败')
          } finally {
            this.submitLoading = false
          }
        }
      })
    },

    // 获取单位列表
    async getOrgList() {
      this.orgLoading = true
      try {
        const response = await findOrganListPage(this.orgQuery)
        this.orgList = response.records || []
        this.orgTotal = response.total || 0
      } catch (error) {
        this.$message.error('获取单位列表失败')
      } finally {
        this.orgLoading = false
      }
    },

    // 单位分页处理
    handleOrgPagination(pagination) {
      this.orgQuery.pager = pagination
      this.getOrgList()
    },

    // 单位选择变化
    handleOrgSelectionChange(selection) {
      this.tempSelectedOrgs = selection
    },

    // 确认选择单位
    confirmSelectOrg() {
      this.selectedOrgs = [...this.tempSelectedOrgs]
      this.selectOrgDialogVisible = false
    },

    // 移除已选单位
    removeSelectedOrg(org) {
      const index = this.selectedOrgs.findIndex(item => item.orgId === org.orgId)
      if (index > -1) {
        this.selectedOrgs.splice(index, 1)
      }
    },

    // 查看开通历史
    async viewHistory(row) {
      this.currentOrgId = row.orgId
      this.historyDialogVisible = true
      this.historyLoading = true

      try {
        const response = await getActiveLog(row.orgId)
        this.historyList = response || []
      } catch (error) {
        this.$message.error('获取开通历史失败')
      } finally {
        this.historyLoading = false
      }
    },

    // 删除会员
    deleteMember(row) {
      this.$confirm('确定要删除该单位的会员信息吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await deleteMemberOrg(row.orgId)
          this.$message.success('删除成功')
          this.getMemberOrgList()
        } catch (error) {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        // 用户取消删除
      })
    }
  }
}
</script>

<style scoped>
.search-column {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 4px;
}

.search-column .fl {
  display: flex;
  align-items: center;
}

.search-column .fr {
  display: flex;
  align-items: center;
}

.search-column__item {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.search-column__item:first-child {
  margin-left: 0;
}

.search-column__label {
  margin-right: 8px;
  white-space: nowrap;
}

.search-column__inner {
  width: 200px;
}

.dialog-footer {
  text-align: right;
}

.warn-tips {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
  line-height: 1.4;
}
</style>
